'use client';

import React, { useState, useEffect } from 'react';
import { useFormContext, Control, FieldError } from 'react-hook-form';
import { School, PlusCircle } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { RHFCombobox, ComboboxOption } from '../RHFCombobox';

export type SchoolSelectorProps = {
  name: string;
  label?: string;
  required?: boolean;
  className?: string;
  onCreateSchool?: () => void;
  showCreateButton?: boolean;
  disabled?: boolean;
  // Additional props for direct integration with React Hook Form
  control?: Control<any>;
  error?: FieldError;
  fetchSchools?: () => Promise<Array<{ id: string; name: string; address?: string }>>;
};

export const SchoolSelector: React.FC<SchoolSelectorProps> = ({
  name,
  label = 'Select School',
  required = false,
  className,
  onCreateSchool,
  showCreateButton = false,
  disabled = false,
  control,
  error,
  fetchSchools,
}) => {
  // Use form context if control is not provided directly
  const formContext = useFormContext();
  const formControl = control || formContext?.control;
  const formError = error || formContext?.formState?.errors?.[name];
  
  const [schools, setSchools] = useState<ComboboxOption[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);

  // Fetch schools on component mount
  useEffect(() => {
    const loadSchools = async () => {
      if (!fetchSchools) {
        setLoadError('fetchSchools function is required');
        return;
      }
      
      setIsLoading(true);
      setLoadError(null);
      try {
        const schoolsData = await fetchSchools();
        const formattedSchools = schoolsData.map(school => ({
          label: school.name,
          value: school.id,
          description: school.address,
        }));
        setSchools(formattedSchools);
      } catch (err) {
        console.error('Error fetching schools:', err);
        setLoadError('Failed to load schools. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    loadSchools();
  }, [fetchSchools]);

  return (
    <div className="space-y-4">
      <div className={`${showCreateButton ? 'flex gap-4 flex-col sm:flex-row' : ''}`}>
        <div className={`relative flex-1 ${className || ''}`}>
          {isLoading ? (
            <div className="p-2 text-sm text-gray-500">Loading schools...</div>
          ) : loadError ? (
            <div className="p-2 text-sm text-red-500">{loadError}</div>
          ) : (
            <RHFCombobox
              name={name}
              label={label}
              options={schools}
              placeholder="Select a school"
              required={required}
              disabled={disabled}
              icon={<School size={18} className="text-gray-500" />}
              noOptionsMessage="No schools found"
              control={formControl}
              error={formError}
          )}
        </div>

        {showCreateButton && onCreateSchool && (
          <div className="flex items-end">
            <Button
              type="button"
              onClick={onCreateSchool}
              className="flex items-center gap-1 h-[42px] mt-auto"
              variant="outline"
              disabled={disabled}
            >
              <PlusCircle size={16} />
              <span>Create School</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default SchoolSelector;