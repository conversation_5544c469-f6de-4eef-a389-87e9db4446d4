'use server';

import { revalidatePath } from 'next/cache';
import {
  updateUser as apiUpdateUser,
  deleteUser as apiDeleteUser,
  getAllUsers as apiGetAllUsers,
  getUserById,
  IUpdateUserPayload,
  IUserResponse,
} from '@/apis/userApi';
import { TTransformResponse, TSuccess, TApiError } from '@/apis/transformResponse';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';
import { User } from 'next-auth';
import { EUserRole } from '@/config/enums/user';

// Define specific success and error types for profile update action
type UpdateProfileSuccess = TSuccess<IUserResponse> & {
  updatedUser: IUserResponse;
  message?: string; // Allow an optional success message
};
type UpdateProfileError = TApiError;
type UpdateProfileResult = UpdateProfileSuccess | UpdateProfileError;

export async function updateUserProfileAction(
  userId: string,
  payload: IUpdateUserPayload
): Promise<UpdateProfileResult> {
  // Ensure this action is called by an authenticated user if necessary
  // For profile updates, the userId should match the logged-in user's ID.
  const session = await getServerSession(authOptions);
  if (!session?.user?.id || session.user.id !== userId) {
    return { status: 'error', message: 'Unauthorized or user ID mismatch.' } as UpdateProfileError;
  }

  try {
    const response = await apiUpdateUser(userId, payload);

    if (response.status === 'success' && response.data) {
      revalidatePath('/profile'); // Revalidate the profile page to show updated data
      return {
        status: 'success',
        data: response.data,
        updatedUser: response.data, // Pass back the updated user for client-side session handling
        message: (response as TSuccess<IUserResponse> & { message?: string }).message || 'Profile updated successfully.', // Access message if available
      } as UpdateProfileSuccess;
    } else if (response.status === 'error') {
      return {
        status: 'error',
        message: response.message || 'Failed to update profile via action.',
      } as UpdateProfileError;
    } else {
      // Should not happen if apiUpdateUser always returns TTransformResponse
      return {
        status: 'error',
        message: 'Unknown error structure from API.',
      } as UpdateProfileError;
    }
  } catch (error: any) {
    console.error('Server action updateUserProfileAction error:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred in the server action.',
    };
  }
}

export async function getUpdatedSessionUser(): Promise<User | null> {
    const session = await getServerSession(authOptions);
    return session?.user || null;
}

// Action to get all users
export async function handleGetAllUsersAction(
  schoolId?: string,
  role?: EUserRole
): Promise<TTransformResponse<IUserResponse[]>> {

  try {
    const response = await apiGetAllUsers(schoolId, role);
    return response;
  } catch (error: any) {
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching users.',
    };
  }
}

type DeleteUserResult = TSuccess<{ id: string }> | TApiError; // Or TSuccess<void> if no data is returned

export async function handleGetUserByIdAction(userId: string): Promise<TTransformResponse<IUserResponse>> {
  try {
    const response = await getUserById(userId);
    return response;
  } catch (error: any) {
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while fetching user by ID.',
    };
  }
}

export async function handleUpdateUserAction(
  userId: string,
  payload: IUpdateUserPayload
): Promise<TTransformResponse<IUserResponse>> {
  try {
    const response = await apiUpdateUser(userId, payload);
    
    if (response.status === 'success') {
      revalidatePath('/users-management');
      revalidatePath('/teacher-management');
    }
    
    return response;
  } catch (error: any) {
    console.error('Server action handleUpdateUserAction error:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred while updating user.',
    };
  }
}

export async function handleDeleteUserAction(userId: string): Promise<DeleteUserResult> {
 
  const session = await getServerSession(authOptions);
  if (!session?.user || (session.user.role !== EUserRole.ADMIN /* && more checks if needed */)) {
     return { status: 'error', message: 'Unauthorized to delete user.' };
  }

  if (session.user.id === userId) {
    return { status: 'error', message: 'Cannot delete your own account through this action.' };
  }

  try {
    const response = await apiDeleteUser(userId);

    if (response.status === 'success') {
      revalidatePath('/users-management');
      revalidatePath('/teacher-management');
      return { status: 'success', data: { id: userId }, message: 'User deleted successfully.' } as TSuccess<{id: string}>;
    } else {
      return {
        status: 'error',
        message: response.message || 'Failed to delete user via action.',
      };
    }
  } catch (error: any) {
    console.error('Server action handleDeleteUserAction error:', error);
    return {
      status: 'error',
      message: error.message || 'An unexpected error occurred in the server action while deleting user.',
    };
  }
}
