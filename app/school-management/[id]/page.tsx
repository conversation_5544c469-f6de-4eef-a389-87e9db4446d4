'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useParams, useRouter } from 'next/navigation'; // useParams to get ID from URL

import DashboardTemplate from '@/components/templates/Dashboard/Dashboard';
import { DetailTemplate } from '@/components/templates/DetailTemplate/DetailTemplate';
import { ListingHeader } from '@/components/molecules/ListingHeader/ListingHeader';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Button } from '@/components/atoms/Button/Button';
import { Label } from '@/components/atoms/Label/Label';
import { SchoolDetailCard } from '@/components/organisms/SchoolDetailCard/SchoolDetailCard';
import { SchoolModal } from '@/components/organisms/SchoolModal/SchoolModal';

import { 
  ArrowLeft, 
  FileText, 
  ExternalLink, 
  Upload, 
  Settings, 
  RefreshCw, 
  Trash, 
  Alert<PERSON>riangle, 
  Eye, 
  FileQuestion, 
  Info 
} from 'lucide-react';

// Actions
import { 
  handleGetSchoolByIdAction, // Use the correct action for fetching school details
  handleDeleteSchoolAction // Add delete school action
} from '@/actions/school.action'; 
import { 
  uploadExaminationFormatAction, 
  getExaminationFormatAction, 
  deleteExaminationFormatAction 
} from '@/actions/examinationFormat.action';
import { TTransformResponse } from '@/apis/transformResponse';
import { ISchoolResponse } from '@/apis/schoolApi'; // Import ISchoolResponse
import { EUserRole } from '@/config/enums/user'; // Import EUserRole for permission check


interface IUploadFormatResponse {
  id: string;
  schoolId: string;
  filename: string;
  uploadedAt: string;
}

interface IGetFormatResponse {
  text?: string;
  url?: string;
  contentType?: string;
}

export default function SchoolDetailPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const schoolId = params?.id as string; // Get school ID from URL

  const [schoolDetails, setSchoolDetails] = useState<ISchoolResponse | null>(null); // Use ISchoolResponse
  const [isLoadingSchool, setIsLoadingSchool] = useState<boolean>(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false); // State for edit modal

  // States for Examination Format Management (similar to the original page)
  const [file, setFile] = useState<File | null>(null);
  const [viewedFormatText, setViewedFormatText] = useState<string | null>(null);
  const [viewedFormatUrl, setViewedFormatUrl] = useState<string | null>(null);
  const [apiMessage, setApiMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isViewing, setIsViewing] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch school details
  const fetchSchoolDetails = useCallback(async () => {
    if (!schoolId || status !== 'authenticated') return;
    setIsLoadingSchool(true);
    setApiMessage(null); // Clear previous messages
    try {
      const response: TTransformResponse<ISchoolResponse> = await handleGetSchoolByIdAction(schoolId);
      if (response.status === 'success' && response.data) {
        setSchoolDetails(response.data);
      } else {
        const message = response.status === 'error' ? (Array.isArray(response.message) ? response.message.join(', ') : response.message) : 'Failed to fetch school details.';
        setApiMessage({ type: 'error', message: message || 'Failed to fetch school details.' });
        setSchoolDetails(null);
      }
    } catch (error: any) {
      setApiMessage({ type: 'error', message: error.message || 'Error fetching school details.' });
      setSchoolDetails(null);
    } finally {
      setIsLoadingSchool(false);
    }
  }, [schoolId, status]);

  useEffect(() => {
    fetchSchoolDetails();
  }, [fetchSchoolDetails]);

  // Handlers for Edit School Modal
  const handleOpenEditModal = () => {
    if (schoolDetails) {
      setIsEditModalOpen(true);
    }
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
  };

  const handleEditSuccess = (updatedSchool: ISchoolResponse | {id: string, name: string}) => {
    fetchSchoolDetails(); // Refresh data
    setApiMessage({ type: 'success', message: 'School details updated successfully!' });
    setIsEditModalOpen(false);
  };

  const handleEditError = (message: string) => {
    setApiMessage({ type: 'error', message: `Failed to update school: ${message}` });
    // Keep modal open or handle as per UX requirements
  };

  // Handler for school deletion
  const handleDeleteSchool = async (schoolId: string) => {
    try {
      const response = await handleDeleteSchoolAction(schoolId);
      if (response.status === 'success') {
        // Show success message and navigate back to school list
        setApiMessage({ type: 'success', message: 'School deleted successfully!' });
        // Short delay to show the success message before navigating
        setTimeout(() => {
          router.push('/school-management');
        }, 1500);
        // return true; // Removed to match Promise<void>
      } else {
        // Show error message
        const message = response.status === 'error' ? 
          (Array.isArray(response.message) ? response.message.join(', ') : response.message) : 
          'Failed to delete school.';
        setApiMessage({ type: 'error', message: message || 'Failed to delete school.' });
        // return false; // Removed to match Promise<void>
      }
    } catch (error: any) {
      setApiMessage({ type: 'error', message: error.message || 'Error deleting school.' });
      // return false; // Removed to match Promise<void>
    }
  };

  // Examination Format Handlers (adapted from app/admin/examination-formats/page.tsx)
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!schoolId) return; // Should always have schoolId here
    if (!file) {
      setApiMessage({ type: 'error', message: 'Please select a file to upload' });
      return;
    }
    setIsUploading(true);
    setApiMessage(null);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('schoolId', schoolId);
      const response: TTransformResponse<IUploadFormatResponse> = await uploadExaminationFormatAction(formData);
      if (response.status === 'success') {
        setApiMessage({ type: 'success', message: 'Examination format uploaded successfully' });
        setFile(null);
        if (fileInputRef.current) fileInputRef.current.value = '';
        // Optionally, refresh the viewed format
        handleView(); 
      } else {
        const message = (Array.isArray(response.message) ? response.message.join(', ') : response.message) || 'Failed to upload examination format';
        setApiMessage({ type: 'error', message });
      }
    } catch (error: any) {
      setApiMessage({ type: 'error', message: error.message || 'Error uploading format' });
    } finally {
      setIsUploading(false);
    }
  };

  const handleView = useCallback(async () => {
    if (!schoolId) return;
    setIsViewing(true);
    setApiMessage(null);
    setViewedFormatText(null);
    setViewedFormatUrl(null);
    try {
      const response: TTransformResponse<IGetFormatResponse> = await getExaminationFormatAction(schoolId);
      if (response.status === 'success' && response.data) {
        if (response.data.url) setViewedFormatUrl(response.data.url);
        else if (response.data.text) setViewedFormatText(response.data.text);
        else setApiMessage({ type: 'error', message: 'No examination format found for this school' });
      } else {
        // Access message only if response is an error type
        const message = response.status === 'error' ? (Array.isArray(response.message) ? response.message.join(', ') : response.message) : 'Failed to retrieve examination format';
        setApiMessage({ type: 'error', message: message || 'Failed to retrieve examination format' });
      }
    } catch (error: any) {
      setApiMessage({ type: 'error', message: error.message || 'Error retrieving format' });
    } finally {
      setIsViewing(false);
    }
  }, [schoolId]);

  // Auto-view format when school details are loaded
  useEffect(() => {
    if (schoolDetails && schoolId) {
      handleView();
    }
  }, [handleView, schoolDetails, schoolId]); // schoolId is already a dependency of handleView, so it's fine here. schoolDetails is a trigger.


  const handleDelete = async () => {
    if (!schoolId) return;
    if (!confirm('Are you sure you want to delete this examination format? This action cannot be undone.')) return;
    setIsDeleting(true);
    setApiMessage(null);
    try {
      const response: TTransformResponse<null> = await deleteExaminationFormatAction(schoolId);
      if (response.status === 'success') {
        setApiMessage({ type: 'success', message: 'Examination format deleted successfully' });
        setViewedFormatText(null);
        setViewedFormatUrl(null);
      } else {
        const message = (Array.isArray(response.message) ? response.message.join(', ') : response.message) || 'Failed to delete examination format';
        setApiMessage({ type: 'error', message });
      }
    } catch (error: any) {
      setApiMessage({ type: 'error', message: error.message || 'Error deleting format' });
    } finally {
      setIsDeleting(false);
    }
  };

  // Render states
  if (status === 'loading' || (isLoadingSchool && !schoolDetails)) {
    return (
      <DashboardTemplate sidebarItems={[]} userMenuDropdownProps={{}} schoolInfo={null}>
        <div className="p-8 flex flex-col items-center justify-center min-h-[60vh]">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading School Details</h2>
          <p className="text-gray-500 mt-2">Please wait while we fetch the information...</p>
        </div>
      </DashboardTemplate>
    );
  }

  if (status === 'unauthenticated') {
    router.push('/auth/sign-in'); // Redirect to login
    return null;
  }

  if (!schoolDetails && !isLoadingSchool) {
     return (
      <DashboardTemplate sidebarItems={[]} userMenuDropdownProps={{}} schoolInfo={null}>
        <div className="p-8 max-w-2xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <div className="flex items-center justify-center text-red-500 bg-red-50 w-16 h-16 rounded-full mx-auto mb-4">
              <Info size={32} />
            </div>
            <h2 className="text-2xl font-bold text-center text-gray-800 mb-4">School Not Found</h2>
            <AlertMessage type="error" message={apiMessage?.message || "The school you're looking for could not be found or failed to load."} />
            <div className="flex justify-center mt-6">
              <Button 
                variant="outline" 
                onClick={() => router.back()} 
                className="flex items-center gap-2"
              >
                <ArrowLeft size={16} />
                Return to School List
              </Button>
            </div>
          </div>
        </div>
      </DashboardTemplate>
    );
  }


  const header = (
    <ListingHeader
      title="School Management"
      subtitle="View and manage school details and resources"
      buttonProps={{
        label: "Back to Schools",
        variant: "outline",
        href: "/school-management",
        className: "flex w-fit items-center gap-1.5",
        iconProps: {
          variant: "arrow-left", // Changed from icon: ArrowLeft
          size: 16
        }
      }}
    />
  );

  const examinationFormatSection = (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden mt-8">
      <div className="bg-gray-100 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center">
          <FileText size={20} className="mr-2 text-gray-600" />
          Examination Format
        </h2>
        {viewedFormatUrl && (
          <a 
            href={viewedFormatUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
          >
            <ExternalLink size={16} className="mr-1.5" />
            Open in new tab
          </a>
        )}
      </div>

      <div className="p-6">
        {apiMessage && (
          <div className="mb-6">
            <AlertMessage type={apiMessage.type} message={apiMessage.message} />
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left column - Upload & Actions */}
          <div className="lg:col-span-1 space-y-6">
            {/* Upload Section */}
            <div className="bg-gray-50 p-5 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                <Upload size={18} className="mr-2 text-gray-600" />
                Upload Format
              </h3>
              <div className="space-y-3">
                <div>
                  <Label htmlFor="file" className="block text-sm font-medium text-gray-700 mb-1">
                    PDF File
                  </Label>
                  <input
                    id="file"
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept=".pdf"
                    className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                </div>
                <Button
                  type="button"
                  variant="primary"
                  onClick={handleUpload}
                  disabled={!file || isUploading}
                  isLoading={isUploading}
                  className="w-full"
                >
                  {isUploading ? 'Uploading...' : 'Upload Format'}
                </Button>
              </div>
            </div>

            {/* Actions Section */}
            <div className="bg-gray-50 p-5 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                <Settings size={18} className="mr-2 text-gray-600" />
                Actions
              </h3>
              <div className="space-y-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleView}
                  disabled={isViewing}
                  isLoading={isViewing}
                  className="w-full flex items-center justify-center gap-2"
                >
                  <RefreshCw size={16} />
                  {isViewing ? 'Loading...' : 'Refresh Format'}
                </Button>

                <Button
                  type="button"
                  variant="error"
                  onClick={handleDelete}
                  disabled={isDeleting}
                  isLoading={isDeleting}
                  className="w-full flex items-center justify-center gap-2"
                >
                  <Trash size={16} />
                  {isDeleting ? 'Deleting...' : 'Delete Format'}
                </Button>

                <div className="bg-amber-50 border border-amber-200 rounded-md p-3 text-xs text-amber-700 mt-3">
                  <div className="flex items-start">
                    <AlertTriangle size={16} className="mr-2 mt-0.5 flex-shrink-0 text-amber-600" />
                    <span>Deleting the examination format cannot be undone. This will permanently remove the format for this school.</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right column - Format Preview */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 p-5 rounded-lg border border-gray-200 shadow-sm h-full">
              <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                <Eye size={18} className="mr-2 text-gray-600" />
                Format Preview
              </h3>

              {viewedFormatUrl && (
                <div className="border border-gray-300 rounded-md bg-white h-[calc(100%-3rem)]">
                  <iframe 
                    src={viewedFormatUrl} 
                    className="w-full h-full min-h-[400px]" 
                    title="Examination Format PDF"
                  />
                </div>
              )}

              {viewedFormatText && (
                <div className="border border-gray-300 rounded-md p-4 bg-white whitespace-pre-wrap font-mono text-sm h-[calc(100%-3rem)] overflow-auto">
                  {viewedFormatText}
                </div>
              )}

              {!viewedFormatUrl && !viewedFormatText && !isViewing && (
                <div className="flex flex-col items-center justify-center h-[400px] text-center border border-gray-200 rounded-md bg-gray-50 p-6">
                  <FileQuestion size={48} className="text-gray-400 mb-4" />
                  <h4 className="text-lg font-medium text-gray-700 mb-2">No Format Available</h4>
                  <p className="text-sm text-gray-500 max-w-md">
                    No examination format has been uploaded for this school yet. Use the upload form to add a format.
                  </p>
                </div>
              )}

              {isViewing && (
                <div className="flex flex-col items-center justify-center h-[400px] text-center border border-gray-200 rounded-md bg-gray-50 p-6">
                  <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
                  <h4 className="text-lg font-medium text-gray-700 mb-2">Loading Format</h4>
                  <p className="text-sm text-gray-500">
                    Please wait while we retrieve the examination format...
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const content = (
    <div className="space-y-6">
      {schoolDetails && (
        <SchoolDetailCard 
          school={schoolDetails} 
          onEdit={handleOpenEditModal}
          onDelete={handleDeleteSchool}
          canDelete={session?.user?.role === EUserRole.SUPER_ADMIN}
        />
      )}
      {examinationFormatSection}
    </div>
  );

  return (
    <DashboardTemplate sidebarItems={[]} userMenuDropdownProps={{}} schoolInfo={null}>
      <DetailTemplate header={header} content={content} />
      {isEditModalOpen && schoolDetails && (
        <SchoolModal
          isOpen={isEditModalOpen}
          onClose={handleCloseEditModal}
          onSuccess={handleEditSuccess}
          onError={handleEditError}
          schoolToEdit={schoolDetails}
        />
      )}
    </DashboardTemplate>
  );
}
